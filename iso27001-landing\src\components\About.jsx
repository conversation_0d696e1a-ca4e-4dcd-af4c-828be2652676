import React from 'react'

const About = () => {
  return (
    <section id="about" className="py-20 bg-gray-50">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-center mb-12 text-gray-900">
            ISO/IEC 27001 คืออะไร?
          </h2>
          
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-12">
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              <strong>ISO/IEC 27001</strong> คือมาตรฐานสากลที่กำหนดข้อกำหนดสำหรับระบบบริหารจัดการความมั่นคงปลอดภัยสารสนเทศ 
              หรือ <strong>Information Security Management System (ISMS)</strong> ซึ่งออกแบบมาเพื่อช่วยให้องค์กรสามารถปกป้องข้อมูลที่สำคัญได้อย่างมีประสิทธิภาพ
            </p>
            
            <div className="grid md:grid-cols-3 gap-6 mt-8">
              <div className="text-center p-6 bg-primary-50 rounded-xl">
                <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-primary-900 mb-2">Confidentiality</h3>
                <p className="text-sm text-gray-600">ความลับของข้อมูล</p>
              </div>
              
              <div className="text-center p-6 bg-primary-50 rounded-xl">
                <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-primary-900 mb-2">Integrity</h3>
                <p className="text-sm text-gray-600">ความถูกต้องสมบูรณ์</p>
              </div>
              
              <div className="text-center p-6 bg-primary-50 rounded-xl">
                <div className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-primary-900 mb-2">Availability</h3>
                <p className="text-sm text-gray-600">ความพร้อมใช้งาน</p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-2xl p-8">
            <h3 className="text-2xl font-bold mb-6">ความสำคัญของ ISO/IEC 27001 สำหรับธุรกิจยุคใหม่</h3>
            <p className="text-lg mb-6 opacity-90">
              ในยุคที่ข้อมูลกลายเป็นทรัพย์สินที่มีค่าที่สุดขององค์กร ความมั่นคงปลอดภัยของข้อมูลไม่ใช่เรื่อง "ทางเลือก" อีกต่อไป 
              แต่กลายเป็น "ข้อบังคับ" ทั้งจากคู่ค้าทางธุรกิจและข้อกฎหมายที่เกี่ยวข้อง เช่น PDPA (กฎหมายคุ้มครองข้อมูลส่วนบุคคล)
            </p>
            
            <div className="grid md:grid-cols-3 gap-6">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>แสดงความมุ่งมั่นในการบริหารจัดการความเสี่ยงด้านข้อมูล</span>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>สร้างความเชื่อมั่นให้แก่ลูกค้า นักลงทุน และพันธมิตร</span>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>รองรับข้อกำหนดด้าน Cybersecurity ที่เพิ่มขึ้นเรื่อยๆ</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
