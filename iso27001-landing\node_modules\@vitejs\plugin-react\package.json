{"name": "@vitejs/plugin-react", "version": "4.0.0", "license": "MIT", "author": "<PERSON>", "contributors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && pnpm run patch-cjs && tsx scripts/copyRefreshUtils.ts", "patch-cjs": "tsx ../../scripts/patchCJS.ts", "prepublishOnly": "npm run build"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-react.git", "directory": "packages/plugin-react"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-react/issues"}, "homepage": "https://github.com/vitejs/vite-plugin-react/tree/main/packages/plugin-react#readme", "dependencies": {"@babel/core": "^7.21.4", "@babel/plugin-transform-react-jsx-self": "^7.21.0", "@babel/plugin-transform-react-jsx-source": "^7.19.6", "react-refresh": "^0.14.0"}, "peerDependencies": {"vite": "^4.2.0"}}