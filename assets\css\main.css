/**
* Template Name: Instant
* Template URL: https://bootstrapmade.com/newtemplate-bootstrap-website-template/
* Updated: Jul 07 2025 with Bootstrap v5.3.7
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# Font & Color Variables
# Help: https://bootstrapmade.com/color-system/
--------------------------------------------------------------*/
/* Fonts */
:root {
  --default-font: "Roboto",  system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Raleway",  sans-serif;
  --nav-font: "Raleway",  sans-serif;
}

/* Global Colors - The following color variables are used throughout the website. Updating them here will change the color scheme of the entire website */
:root { 
  --background-color: #040000; /* Background color for the entire website, including individual sections */
  --default-color: #f8f8f8; /* Default color used for the majority of the text content across the entire website */
  --heading-color: #ffffff; /* Color for headings, subheadings and title throughout the website */
  --accent-color: #e59d02; /* Accent color that represents your brand on the website. It's used for buttons, links, and other elements that need to stand out */
  --surface-color: #191919; /* The surface color is used as a background of boxed elements within sections, such as cards, icon boxes, or other elements that require a visual separation from the global background. */
  --contrast-color: #ffffff; /* Contrast color for text, ensuring readability against backgrounds of accent, heading, or default colors. */
}

/* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
:root {
  --nav-color: #f8f8f8;  /* The default color of the main navmenu links */
  --nav-hover-color: #e59d02; /* Applied to main navmenu links when they are hovered over or active */
  --nav-mobile-background-color: #1b1a1a; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #1b1a1a; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #f8f8f8; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #e59d02; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */

.light-background {
  --background-color: #1b1a1a;
  --surface-color: #282828;
}

.dark-background {
  --background-color: #000000;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: #1b1b1b;
  --contrast-color: #ffffff;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/* PHP Email Form Messages
------------------------------*/
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  --background-color: rgba(255, 255, 255, 0);
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 15px 0;
  transition: all 0.5s;
  z-index: 997;
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 36px;
  margin-right: 8px;
}

.header .logo h1 {
  font-size: 30px;
  margin: 0;
  font-weight: 700;
  color: var(--heading-color);
}

.header .btn-getstarted,
.header .btn-getstarted:focus {
  color: var(--contrast-color);
  background: var(--accent-color);
  font-size: 14px;
  padding: 8px 26px;
  margin: 0;
  border-radius: 50px;
  transition: 0.3s;
}

.header .btn-getstarted:hover,
.header .btn-getstarted:focus:hover {
  color: var(--contrast-color);
  background: color-mix(in srgb, var(--accent-color), transparent 15%);
}

@media (max-width: 1200px) {
  .header .logo {
    order: 1;
  }

  .header .btn-getstarted {
    order: 2;
    margin: 0 15px 0 0;
    padding: 6px 20px;
  }

  .header .navmenu {
    order: 3;
  }
}

.scrolled .header::before {
  content: "";
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: -1;
  pointer-events: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  /* optional border */
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Navmenu - Desktop */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    padding: 18px 15px;
    font-size: 16px;
    font-family: var(--nav-font);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu li:last-child a {
    padding-right: 0;
  }

  .navmenu li:hover>a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 14px;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 4px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover>a {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .dropdown:hover>ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover>ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Navmenu - Mobile */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 28px;
    line-height: 0;
    margin-right: 10px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navmenu {
    padding: 0;
    z-index: 9997;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 60px 20px 20px 20px;
    padding: 10px 0;
    margin: 0;
    border-radius: 6px;
    background-color: var(--nav-mobile-background-color);
    overflow-y: auto;
    transition: 0.3s;
    z-index: 9998;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-dropdown-color);
    padding: 10px 20px;
    font-family: var(--nav-font);
    font-size: 17px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: 0.3s;
    background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(33, 37, 41, 0.1);
  }

  .navmenu .dropdown>.dropdown-active {
    display: block;
    background-color: rgba(33, 37, 41, 0.03);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: absolute;
    font-size: 32px;
    top: 15px;
    right: 15px;
    margin-right: 0;
    z-index: 9999;
  }

  .mobile-nav-active .navmenu {
    position: fixed;
    overflow: hidden;
    inset: 0;
    background: rgba(33, 37, 41, 0.8);
    transition: 0.3s;
  }

  .mobile-nav-active .navmenu>ul {
    display: block;
  }
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: var(--default-color);
  background-color: var(--background-color);
  font-size: 14px;
  position: relative;
}

.footer .footer-top {
  background-color: color-mix(in srgb, var(--background-color), white 5%);
  padding-top: 50px;
}

.footer .footer-about .logo {
  line-height: 1;
  margin-bottom: 25px;
}

.footer .footer-about .logo img {
  max-height: 40px;
  margin-right: 6px;
}

.footer .footer-about .logo span {
  color: var(--heading-color);
  font-family: var(--heading-font);
  font-size: 26px;
  font-weight: 700;
  letter-spacing: 1px;
}

.footer .footer-about p {
  font-size: 14px;
  font-family: var(--heading-font);
}

.footer h4 {
  font-size: 16px;
  font-weight: 600;
  position: relative;
  padding-bottom: 12px;
  margin-bottom: 15px;
}

.footer h4::after {
  content: "";
  position: absolute;
  display: block;
  width: 20px;
  height: 2px;
  background: var(--accent-color);
  bottom: 0;
  left: 0;
}

.footer .footer-links {
  margin-bottom: 30px;
}

.footer .footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer .footer-links ul i {
  padding-right: 2px;
  font-size: 12px;
  line-height: 0;
}

.footer .footer-links ul li {
  padding: 10px 0;
  display: flex;
  align-items: center;
}

.footer .footer-links ul li:first-child {
  padding-top: 0;
}

.footer .footer-links ul a {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  display: inline-block;
  line-height: 1;
}

.footer .footer-links ul a:hover {
  color: var(--accent-color);
}

.footer .footer-contact p {
  margin-bottom: 5px;
}

.footer .copyright {
  padding: 30px 0;
}

.footer .copyright p {
  margin-bottom: 0;
}

.footer .credits {
  margin-top: 5px;
  font-size: 13px;
}

.footer .social-links a {
  font-size: 18px;
  display: inline-block;
  background: color-mix(in srgb, var(--default-color), transparent 92%);
  color: var(--accent-color);
  line-height: 1;
  padding: 8px 0;
  margin-right: 4px;
  border-radius: 4px;
  text-align: center;
  width: 36px;
  height: 36px;
  transition: 0.3s;
}

.footer .social-links a:hover {
  color: var(--contrast-color);
  background: var(--accent-color);
  text-decoration: none;
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
  bottom: 15px;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 170px 0 80px 0;
  text-align: center;
  position: relative;
}

.page-title h1 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 10px;
}

.page-title .breadcrumbs ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  justify-content: center;
  padding: 0;
  margin: 0;
  font-size: 16px;
  font-weight: 400;
}

.page-title .breadcrumbs ol li+li {
  padding-left: 10px;
}

.page-title .breadcrumbs ol li+li::before {
  content: "/";
  display: inline-block;
  padding-right: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  scroll-margin-top: 90px;
  overflow: clip;
}

@media (max-width: 1199px) {

  section,
  .section {
    scroll-margin-top: 66px;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding-bottom: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 32px;
  font-weight: 700;
  position: relative;
}

.section-title h2:before,
.section-title h2:after {
  content: "";
  width: 50px;
  height: 2px;
  background: var(--accent-color);
  display: inline-block;
}

.section-title h2:before {
  margin: 0 15px 10px 0;
}

.section-title h2:after {
  margin: 0 0 10px 15px;
}

.section-title p {
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero {
  padding-top: 120px;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, color-mix(in srgb, var(--accent-color), transparent 96%) 0%, var(--background-color) 40%, color-mix(in srgb, var(--accent-color), transparent 98%) 100%);
  padding-bottom: 60px;
}

.hero::before {
  content: "";
  position: absolute;
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, color-mix(in srgb, var(--accent-color), transparent 85%) 0%, transparent 70%);
  border-radius: 50%;
  top: -20%;
  right: -10%;
  animation: float 6s ease-in-out infinite;
  z-index: 1;
}

.hero::after {
  content: "";
  position: absolute;
  width: 200px;
  height: 200px;
  background: color-mix(in srgb, var(--accent-color), transparent 88%);
  border-radius: 50%;
  bottom: 10%;
  left: 5%;
  filter: blur(40px);
  animation: float 4s ease-in-out infinite reverse;
  z-index: 1;
}

.hero .hero-wrapper {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.hero .hero-title {
  font-size: 48px;
  font-weight: 900;
  color: var(--heading-color);
  line-height: 1.1;
  margin-bottom: 25px;
  letter-spacing: -0.02em;
}

@media (max-width: 768px) {
  .hero .hero-title {
    font-size: 36px;
  }
}

@media (max-width: 575px) {
  .hero .hero-title {
    font-size: 28px;
  }
}

.hero .hero-title .typed {
  background: linear-gradient(135deg, var(--accent-color) 0%, color-mix(in srgb, var(--accent-color), #8b5cf6 50%) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hero .hero-title .typed::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--accent-color) 0%, color-mix(in srgb, var(--accent-color), #8b5cf6 50%) 100%);
  border-radius: 2px;
}

.hero .hero-description {
  font-size: 1.2rem;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero .hero-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 60px;
}

.hero .hero-actions .action-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 16px 32px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.hero .hero-actions .action-btn.primary {
  background: var(--accent-color);
  color: var(--contrast-color);
  border: 2px solid var(--accent-color);
}

.hero .hero-actions .action-btn.primary::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, color-mix(in srgb, var(--accent-color), #8b5cf6 30%) 0%, var(--accent-color) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hero .hero-actions .action-btn.primary span,
.hero .hero-actions .action-btn.primary i {
  position: relative;
  z-index: 1;
}

.hero .hero-actions .action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px color-mix(in srgb, var(--accent-color), transparent 60%);
}

.hero .hero-actions .action-btn.primary:hover::before {
  opacity: 1;
}

.hero .hero-actions .action-btn.secondary {
  background: transparent;
  color: var(--default-color);
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 80%);
}

.hero .hero-actions .action-btn.secondary:hover {
  background: var(--surface-color);
  border-color: var(--accent-color);
  color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px color-mix(in srgb, var(--default-color), transparent 90%);
}

.hero .hero-image-showcase {
  position: relative;
}

.hero .hero-image-showcase .image-wrapper {
  position: relative;
  background: var(--surface-color);
  border-radius: 20px;
  padding: 15px;
  box-shadow: 0 20px 60px color-mix(in srgb, var(--default-color), transparent 88%);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 92%);
}

.hero .hero-image-showcase .image-wrapper img {
  border-radius: 15px;
  width: 100%;
  height: auto;
}

.hero .hero-image-showcase .floating-card {
  position: absolute;
  background: var(--surface-color);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 30px color-mix(in srgb, var(--default-color), transparent 85%);
  backdrop-filter: blur(10px);
}

.hero .hero-image-showcase .floating-card.card-1 {
  top: 20%;
  left: -60px;
  animation: float 3s ease-in-out infinite;
}

.hero .hero-image-showcase .floating-card.card-2 {
  bottom: 15%;
  right: -60px;
  animation: float 3s ease-in-out infinite reverse;
}

.hero .hero-image-showcase .floating-card .card-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.hero .hero-image-showcase .floating-card .card-content .card-icon {
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero .hero-image-showcase .floating-card .card-content .card-icon i {
  color: var(--accent-color);
  font-size: 20px;
}

.hero .hero-image-showcase .floating-card .card-content .card-info h4 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--heading-color);
  margin: 0;
  line-height: 1;
}

.hero .hero-image-showcase .floating-card .card-content .card-info p {
  margin: 5px 0 0;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-size: 0.85rem;
  white-space: nowrap;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-15px);
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 0.4;
  }

  50% {
    opacity: 0.8;
  }
}

@media (max-width: 991px) {
  .hero {
    min-height: auto;
  }

  .hero .floating-card.card-1 {
    left: -30px;
    top: 10%;
  }

  .hero .floating-card.card-2 {
    right: -30px;
    bottom: 10%;
  }

  .hero .hero-actions {
    margin-bottom: 40px;
  }
}

@media (max-width: 768px) {
  .hero .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .hero .hero-actions .action-btn {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .hero .floating-card {
    display: none;
  }
}

@media (max-width: 576px) {
  .hero {
    padding: 100px 0 30px;
  }

  .hero .hero-badge {
    margin-bottom: 20px;
  }

  .hero .hero-description {
    font-size: 1.1rem;
    margin-bottom: 30px;
  }

  .hero .hero-actions {
    margin-bottom: 30px;
  }
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .image-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease-in-out;
}

.about .image-wrapper:hover {
  transform: translateY(-5px);
}

.about .content h2 {
  font-weight: 700;
  position: relative;
  display: inline-block;
}

.about .content h2:after {
  content: "";
  position: absolute;
  width: 50px;
  height: 3px;
  background-color: var(--accent-color);
  left: 0;
  bottom: -10px;
}

.about .content h5 {
  font-weight: 500;
  color: color-mix(in srgb, var(--heading-color), transparent 20%);
}

.about .content p {
  font-size: 1rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
}

.about .features-list {
  margin-top: 2rem;
}

.about .features-list .feature-item {
  padding: 20px;
  background-color: var(--surface-color);
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
}

.about .features-list .feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.about .features-list .feature-item i {
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 15px;
  display: block;
}

.about .features-list .feature-item h5 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.about .features-list .feature-item p {
  font-size: 0.9rem;
  margin-bottom: 0;
}

.about .btn {
  padding: 10px 25px;
  border-radius: 50px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.about .btn.btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.about .btn.btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), #000 10%);
  border-color: color-mix(in srgb, var(--accent-color), #000 10%);
}

.about .btn.btn-outline-primary {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.about .btn.btn-outline-primary:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

@media (max-width: 992px) {
  .about .image-wrapper {
    margin-bottom: 2rem;
  }

  .about .content {
    text-align: center;
  }

  .about .content h2:after {
    left: 50%;
    transform: translateX(-50%);
  }

  .about .signature-area {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .about .features-list .feature-item {
    margin-bottom: 1.5rem;
  }
}

/*--------------------------------------------------------------
# Features Section
--------------------------------------------------------------*/
.features .intro-content h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 25px;
  color: var(--heading-color);
  line-height: 1.3;
}

.features .intro-content p {
  font-size: 18px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 35px;
  line-height: 1.6;
}

.features .intro-content .feature-stats {
  display: flex;
  gap: 30px;
}

.features .intro-content .feature-stats .stat-item {
  text-align: center;
}

.features .intro-content .feature-stats .stat-item .stat-number {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.features .intro-content .feature-stats .stat-item .stat-label {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  font-weight: 500;
  margin-top: 5px;
  display: block;
}

.features .intro-image {
  position: relative;
}

.features .intro-image img {
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.features .intro-image::before {
  content: "";
  position: absolute;
  top: -20px;
  left: -20px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), transparent 40%));
  border-radius: 50%;
  z-index: -1;
  opacity: 0.7;
}

.features .intro-image::after {
  content: "";
  position: absolute;
  bottom: -30px;
  right: -30px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--heading-color), color-mix(in srgb, var(--heading-color), transparent 40%));
  border-radius: 20px;
  z-index: -1;
  opacity: 0.5;
}

.features .features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 60px;
}

.features .feature-item {
  position: relative;
  background: var(--surface-color);
  padding: 35px;
  border-radius: 20px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  overflow: hidden;
}

.features .feature-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), color-mix(in srgb, var(--accent-color), transparent 50%));
  transition: left 0.5s ease;
}

.features .feature-item .feature-number {
  position: absolute;
  top: 20px;
  right: 25px;
  font-size: 48px;
  font-weight: 900;
  color: color-mix(in srgb, var(--accent-color), transparent 90%);
  line-height: 1;
  z-index: 1;
}

.features .feature-item .feature-content {
  position: relative;
  z-index: 2;
}

.features .feature-item .feature-content .feature-icon {
  margin-bottom: 25px;
}

.features .feature-item .feature-content .feature-icon i {
  font-size: 32px;
  color: var(--contrast-color);
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), transparent 30%));
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px color-mix(in srgb, var(--accent-color), transparent 70%);
}

.features .feature-item .feature-content h4 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 18px;
  color: var(--heading-color);
  line-height: 1.3;
}

.features .feature-item .feature-content p {
  font-size: 15px;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  margin-bottom: 25px;
  line-height: 1.6;
}

.features .feature-item .feature-content .feature-tags {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.features .feature-item .feature-content .feature-tags .tag {
  font-size: 12px;
  font-weight: 600;
  padding: 6px 12px;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.features .feature-item:hover {
  transform: translateY(-15px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.features .feature-item:hover::before {
  left: 0;
}

.features .feature-item:hover .feature-number {
  color: color-mix(in srgb, var(--accent-color), transparent 80%);
  transform: scale(1.1);
}

.features .feature-item:hover .feature-icon i {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 35px color-mix(in srgb, var(--accent-color), transparent 60%);
}

.features .feature-item:hover .feature-tags .tag {
  background: var(--accent-color);
  color: var(--contrast-color);
}

@media (max-width: 991px) {
  .features .intro-content {
    margin-bottom: 40px;
  }

  .features .intro-content h2 {
    font-size: 32px;
  }

  .features .intro-content .feature-stats {
    justify-content: center;
    gap: 25px;
  }

  .features .features-grid {
    grid-template-columns: 1fr;
    margin-top: 40px;
    gap: 25px;
  }
}

@media (max-width: 767px) {
  .features .intro-content .feature-stats {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .features .intro-content .feature-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .features .intro-content .feature-stats .stat-item .stat-number {
    font-size: 24px;
  }

  .features .intro-content .feature-stats .stat-item .stat-label {
    margin-top: 0;
  }

  .features .feature-item {
    padding: 25px;
  }

  .features .feature-item .feature-number {
    font-size: 36px;
    top: 15px;
    right: 20px;
  }

  .features .feature-item .feature-content h4 {
    font-size: 20px;
  }

  .features .features-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 575px) {
  .features .intro-content h2 {
    font-size: 28px;
  }

  .features .intro-content p {
    font-size: 16px;
  }
}

/*--------------------------------------------------------------
# Features Tabs Section
--------------------------------------------------------------*/
.features-tabs .tabs-wrapper {
  background-color: var(--surface-color);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px color-mix(in srgb, var(--default-color), transparent 90%);
}

.features-tabs .nav-tabs {
  border: 0;
  gap: 20px;
  margin-bottom: 40px;
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 992px) {
  .features-tabs .nav-tabs {
    flex-direction: column;
    gap: 15px;
  }
}

.features-tabs .nav-item {
  flex: 1;
  min-width: 0;
}

@media (max-width: 992px) {
  .features-tabs .nav-item {
    flex: none;
  }
}

.features-tabs .nav-link {
  background: color-mix(in srgb, var(--surface-color), var(--default-color) 3%);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 85%);
  border-radius: 15px;
  padding: 25px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: auto;
  min-height: 80px;
  cursor: pointer;
}

.features-tabs .nav-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), var(--heading-color) 20%));
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

@media (max-width: 768px) {
  .features-tabs .nav-link {
    padding: 20px 15px;
    min-height: 70px;
  }
}

.features-tabs .nav-link .tab-icon {
  position: relative;
  z-index: 2;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), var(--heading-color) 20%));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.features-tabs .nav-link .tab-icon i {
  font-size: 24px;
  color: var(--contrast-color);
}

@media (max-width: 768px) {
  .features-tabs .nav-link .tab-icon {
    width: 40px;
    height: 40px;
  }

  .features-tabs .nav-link .tab-icon i {
    font-size: 20px;
  }
}

.features-tabs .nav-link .tab-content {
  position: relative;
  z-index: 2;
  flex: 1;
}

.features-tabs .nav-link .tab-content h5 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
  transition: color 0.4s ease;
}

@media (max-width: 768px) {
  .features-tabs .nav-link .tab-content h5 {
    font-size: 14px;
  }
}

.features-tabs .nav-link .tab-content span {
  font-size: 13px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  transition: color 0.4s ease;
}

@media (max-width: 768px) {
  .features-tabs .nav-link .tab-content span {
    font-size: 12px;
  }
}

.features-tabs .nav-link:hover {
  transform: translateY(-3px);
  border-color: transparent;
  box-shadow: 0 15px 40px color-mix(in srgb, var(--accent-color), transparent 70%);
}

.features-tabs .nav-link:hover::before {
  opacity: 1;
}

.features-tabs .nav-link:hover .tab-icon {
  background: var(--contrast-color);
  box-shadow: 0 8px 25px color-mix(in srgb, var(--default-color), transparent 80%);
}

.features-tabs .nav-link:hover .tab-icon i {
  color: var(--accent-color);
}

.features-tabs .nav-link:hover .tab-content h5,
.features-tabs .nav-link:hover .tab-content span {
  color: var(--contrast-color);
}

.features-tabs .nav-link.active {
  border-color: var(--accent-color);
  box-shadow: 0 15px 40px color-mix(in srgb, var(--accent-color), transparent 70%);
}

.features-tabs .nav-link.active::before {
  opacity: 1;
}

.features-tabs .nav-link.active .tab-icon {
  background: var(--contrast-color);
}

.features-tabs .nav-link.active .tab-icon i {
  color: var(--accent-color);
}

.features-tabs .nav-link.active .tab-content h5,
.features-tabs .nav-link.active .tab-content span {
  color: var(--contrast-color);
}

.features-tabs .tab-content .tab-pane {
  padding: 40px 0;
}

@media (max-width: 768px) {
  .features-tabs .tab-content .tab-pane {
    padding: 20px 0;
  }
}

.features-tabs .content-wrapper {
  padding-right: 40px;
}

@media (max-width: 992px) {
  .features-tabs .content-wrapper {
    padding-right: 0;
    margin-bottom: 40px;
  }
}

.features-tabs .content-wrapper .icon-badge {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), var(--heading-color) 20%));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  box-shadow: 0 10px 30px color-mix(in srgb, var(--accent-color), transparent 70%);
}

.features-tabs .content-wrapper .icon-badge i {
  font-size: 30px;
  color: var(--contrast-color);
}

.features-tabs .content-wrapper h3 {
  font-size: 32px;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 20px;
  line-height: 1.2;
}

@media (max-width: 768px) {
  .features-tabs .content-wrapper h3 {
    font-size: 24px;
  }
}

.features-tabs .content-wrapper p {
  font-size: 16px;
  line-height: 1.7;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 30px;
}

.features-tabs .content-wrapper .feature-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 35px;
}

@media (max-width: 576px) {
  .features-tabs .content-wrapper .feature-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.features-tabs .content-wrapper .feature-grid .feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.features-tabs .content-wrapper .feature-grid .feature-item i {
  font-size: 16px;
  color: var(--accent-color);
  flex-shrink: 0;
}

.features-tabs .content-wrapper .feature-grid .feature-item span {
  font-size: 14px;
  color: var(--default-color);
  line-height: 1.4;
}

.features-tabs .content-wrapper .stats-row {
  display: flex;
  gap: 30px;
  margin-bottom: 35px;
}

@media (max-width: 576px) {
  .features-tabs .content-wrapper .stats-row {
    gap: 20px;
  }
}

.features-tabs .content-wrapper .stats-row .stat-item {
  text-align: center;
}

.features-tabs .content-wrapper .stats-row .stat-item .stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--accent-color);
  margin-bottom: 5px;
}

@media (max-width: 576px) {
  .features-tabs .content-wrapper .stats-row .stat-item .stat-number {
    font-size: 20px;
  }
}

.features-tabs .content-wrapper .stats-row .stat-item .stat-label {
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.features-tabs .content-wrapper .btn-primary {
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), var(--heading-color) 20%));
  color: var(--contrast-color);
  padding: 15px 35px;
  border-radius: 50px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  border: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px color-mix(in srgb, var(--accent-color), transparent 70%);
}

.features-tabs .content-wrapper .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px color-mix(in srgb, var(--accent-color), transparent 60%);
  color: var(--contrast-color);
}

.features-tabs .content-wrapper .btn-primary:hover i {
  transform: translateX(5px);
}

.features-tabs .content-wrapper .btn-primary i {
  transition: transform 0.3s ease;
}

.features-tabs .visual-content {
  position: relative;
}

.features-tabs .visual-content .main-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px color-mix(in srgb, var(--default-color), transparent 85%);
}

.features-tabs .visual-content .main-image img {
  width: 100%;
  height: auto;
  transition: transform 0.6s ease;
}

.features-tabs .visual-content .main-image:hover img {
  transform: scale(1.05);
}

.features-tabs .visual-content .floating-card {
  position: absolute;
  bottom: 30px;
  left: 30px;
  background: var(--surface-color);
  padding: 20px 25px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 15px 40px color-mix(in srgb, var(--default-color), transparent 80%);
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.6s forwards;
}

@media (max-width: 576px) {
  .features-tabs .visual-content .floating-card {
    bottom: 20px;
    left: 20px;
    padding: 15px 20px;
    gap: 10px;
  }
}

.features-tabs .visual-content .floating-card i {
  font-size: 28px;
  color: var(--accent-color);
}

@media (max-width: 576px) {
  .features-tabs .visual-content .floating-card i {
    font-size: 24px;
  }
}

.features-tabs .visual-content .floating-card .card-content span {
  display: block;
  font-size: 12px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 2px;
}

.features-tabs .visual-content .floating-card .card-content strong {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
}

@media (max-width: 576px) {
  .features-tabs .visual-content .floating-card .card-content strong {
    font-size: 14px;
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/
.services {
  padding: 80px 0;
}

.services .intro-content .section-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  padding: 8px 20px;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.services .intro-content .section-badge i {
  font-size: 12px;
}

.services .intro-content .section-heading {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--heading-color);
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .services .intro-content .section-heading {
    font-size: 2.2rem;
  }
}

.services .intro-content .section-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 2rem;
}

.services .intro-content .cta-button {
  display: inline-block;
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 15px 35px;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px color-mix(in srgb, var(--accent-color), transparent 70%);
}

.services .intro-content .cta-button:hover {
  background: color-mix(in srgb, var(--accent-color), black 10%);
  color: var(--contrast-color);
  transform: translateY(-3px);
  box-shadow: 0 12px 35px color-mix(in srgb, var(--accent-color), transparent 60%);
}

.services .hero-visual {
  position: relative;
}

.services .hero-visual img {
  border-radius: 20px;
  box-shadow: 0 20px 60px color-mix(in srgb, var(--default-color), transparent 85%);
}

@media (max-width: 991px) {
  .services .hero-visual {
    margin-top: 3rem;
  }
}

.services .services-grid .service-card {
  background: var(--surface-color);
  padding: 2rem;
  border-radius: 15px;
  height: 100%;
  transition: all 0.3s ease;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  position: relative;
  overflow: hidden;
}

.services .services-grid .service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--accent-color);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.services .services-grid .service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px color-mix(in srgb, var(--default-color), transparent 80%);
}

.services .services-grid .service-card:hover::before {
  transform: scaleX(1);
}

.services .services-grid .service-card:hover .card-number span {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.services .services-grid .service-card .card-number {
  margin-bottom: 1.5rem;
}

.services .services-grid .service-card .card-number span {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  border-radius: 15px;
  font-size: 1.5rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.services .services-grid .service-card .card-content .service-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.services .services-grid .service-card .card-content .service-title a {
  color: var(--heading-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.services .services-grid .service-card .card-content .service-title a:hover {
  color: var(--accent-color);
}

.services .services-grid .service-card .card-content .service-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .services {
    padding: 60px 0;
  }

  .services .intro-content {
    text-align: center;
    margin-bottom: 2rem;
  }

  .services .services-grid .service-card {
    padding: 1.5rem;
  }
}

/*--------------------------------------------------------------
# Call To Action Section
--------------------------------------------------------------*/
.call-to-action {
  position: relative;
  padding: 3rem;
  border-radius: 1rem;
  overflow: hidden;
}

.call-to-action .badge {
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 2rem;
}

.call-to-action h2 {
  font-size: clamp(1.75rem, 3vw, 2.5rem);
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
}

.call-to-action p {
  font-size: 1.125rem;
  line-height: 1.6;
  opacity: 0.9;
}

.call-to-action .features .feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--surface-color);
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.call-to-action .features .feature-item:hover {
  transform: translateY(-2px);
}

.call-to-action .features .feature-item i {
  color: var(--accent-color);
  font-size: 1.25rem;
}

.call-to-action .features .feature-item span {
  font-weight: 500;
}

.call-to-action .cta-buttons .btn {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  border-radius: 50px;
}

.call-to-action .cta-buttons .btn.btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.call-to-action .cta-buttons .btn.btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  border-color: color-mix(in srgb, var(--accent-color), transparent 15%);
  transform: translateY(-2px);
}

.call-to-action .cta-buttons .btn.btn-outline {
  border: 2px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  color: var(--accent-color);
}

.call-to-action .cta-buttons .btn.btn-outline:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-2px);
}

.call-to-action .content-right {
  flex-shrink: 0;
  max-width: 100%;
  width: 450px;
}

.call-to-action .content-right img {
  width: 100%;
  height: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.call-to-action .content-right .floating-card {
  position: absolute;
  bottom: 2rem;
  right: -1rem;
  background: var(--surface-color);
  padding: 1rem;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: float 3s ease-in-out infinite;
}

.call-to-action .content-right .floating-card .card-icon {
  width: 3rem;
  height: 3rem;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call-to-action .content-right .floating-card .card-icon i {
  color: var(--accent-color);
  font-size: 1.5rem;
}

.call-to-action .content-right .floating-card .card-content {
  display: flex;
  flex-direction: column;
}

.call-to-action .content-right .floating-card .card-content .stats-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--accent-color);
}

.call-to-action .content-right .floating-card .card-content .stats-text {
  font-size: 0.875rem;
  opacity: 0.8;
}

.call-to-action .decoration {
  position: absolute;
  inset: 0;
  pointer-events: none;
  overflow: hidden;
  z-index: 1;
}

.call-to-action .decoration .circle-1,
.call-to-action .decoration .circle-2 {
  position: absolute;
  border-radius: 50%;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
}

.call-to-action .decoration .circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  opacity: 0.5;
}

.call-to-action .decoration .circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  opacity: 0.3;
}

@media (max-width: 991.98px) {
  .call-to-action {
    padding: 2rem;
  }

  .call-to-action .content-right {
    width: 100%;
    margin-top: 2rem;
  }

  .call-to-action .content-right .floating-card {
    position: relative;
    bottom: auto;
    right: auto;
    margin-top: -3rem;
    margin-right: 1rem;
    z-index: 1;
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials {
  position: relative;
  overflow: hidden;
  /* Swiper Navigation */
  /* Swiper Pagination */
  /* Responsive Styles */
}

.testimonials .testimonial-slider {
  position: relative;
  padding-bottom: 50px;
}

.testimonials .testimonial-slider .swiper-wrapper {
  height: auto !important;
}

.testimonials .testimonial-item {
  background: linear-gradient(135deg, var(--surface-color) 0%, color-mix(in srgb, var(--surface-color), var(--accent-color) 2%) 100%);
  border-radius: 20px;
  padding: 0;
  height: 100%;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.4s ease;
  overflow: hidden;
  position: relative;
}

.testimonials .testimonial-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color), color-mix(in srgb, var(--accent-color), var(--heading-color) 30%));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.testimonials .testimonial-item:hover {
  border-color: var(--accent-color);
}

.testimonials .testimonial-item:hover::before {
  transform: scaleX(1);
}

.testimonials .testimonial-item:hover .testimonial-header img {
  transform: scale(1.05);
}

.testimonials .testimonial-item:hover .quote-icon {
  color: var(--accent-color);
  transform: scale(1.1);
}

.testimonials .testimonial-header {
  position: relative;
  text-align: center;
  padding: 30px 30px 20px;
  background: linear-gradient(135deg, color-mix(in srgb, var(--surface-color), var(--accent-color) 3%) 0%, var(--surface-color) 100%);
}

.testimonials .testimonial-header img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid color-mix(in srgb, var(--accent-color), transparent 70%);
  margin-bottom: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.testimonials .testimonial-header .rating {
  display: flex;
  justify-content: center;
  gap: 3px;
}

.testimonials .testimonial-header .rating i {
  color: #ffc107;
  font-size: 0.9rem;
}

.testimonials .testimonial-body {
  padding: 0 30px 20px;
}

.testimonials .testimonial-body p {
  font-size: 1rem;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin: 0;
  font-style: italic;
  text-align: center;
  position: relative;
}

.testimonials .testimonial-body p::before,
.testimonials .testimonial-body p::after {
  content: '"';
  font-size: 1.5rem;
  color: var(--accent-color);
  opacity: 0.6;
  font-family: serif;
  position: absolute;
}

.testimonials .testimonial-body p::before {
  top: -5px;
  left: -10px;
}

.testimonials .testimonial-body p::after {
  bottom: -20px;
  right: -5px;
}

.testimonials .testimonial-footer {
  padding: 20px 30px 30px;
  text-align: center;
  position: relative;
}

.testimonials .testimonial-footer h5 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 0 0 5px;
}

.testimonials .testimonial-footer span {
  font-size: 0.85rem;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  display: block;
  margin-bottom: 15px;
}

.testimonials .testimonial-footer .quote-icon {
  position: absolute;
  bottom: 15px;
  right: 25px;
  color: color-mix(in srgb, var(--accent-color), transparent 60%);
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.testimonials .swiper-navigation {
  position: relative;
  margin-top: 25px;
  display: flex;
  justify-content: flex-end;
}

.testimonials .swiper-button-prev,
.testimonials .swiper-button-next {
  position: static;
  width: 45px;
  height: 45px;
  margin: 0 10px;
  background: var(--accent-color);
  border-radius: 50%;
  color: var(--contrast-color);
  font-size: 16px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.testimonials .swiper-button-prev:hover,
.testimonials .swiper-button-next:hover {
  background: color-mix(in srgb, var(--accent-color), var(--heading-color) 20%);
  transform: scale(1.05);
}

.testimonials .swiper-button-prev::after,
.testimonials .swiper-button-next::after {
  font-size: 16px;
  font-weight: 600;
}

.testimonials .swiper-pagination {
  position: static;
  margin-top: 30px;
  text-align: center;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: color-mix(in srgb, var(--default-color), transparent 70%);
  opacity: 1;
  margin: 0 6px;
  transition: all 0.3s ease;
}

.testimonials .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--accent-color);
  transform: scale(1.2);
}

@media (max-width: 1199px) {
  .testimonials .testimonial-item .testimonial-header {
    padding: 25px 25px 15px;
  }

  .testimonials .testimonial-item .testimonial-header img {
    width: 70px;
    height: 70px;
  }

  .testimonials .testimonial-item .testimonial-body,
  .testimonials .testimonial-item .testimonial-footer {
    padding-left: 25px;
    padding-right: 25px;
  }
}

@media (max-width: 991px) {
  .testimonials .testimonial-item {
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  .testimonials .testimonial-item .testimonial-header {
    padding: 20px 20px 10px;
  }

  .testimonials .testimonial-item .testimonial-header img {
    width: 60px;
    height: 60px;
  }

  .testimonials .testimonial-item .testimonial-header .rating i {
    font-size: 0.8rem;
  }

  .testimonials .testimonial-item .testimonial-body {
    padding: 0 20px 15px;
  }

  .testimonials .testimonial-item .testimonial-body p {
    font-size: 0.95rem;
  }

  .testimonials .testimonial-item .testimonial-footer {
    padding: 15px 20px 20px;
  }

  .testimonials .testimonial-item .testimonial-footer h5 {
    font-size: 1rem;
  }

  .testimonials .testimonial-item .testimonial-footer span {
    font-size: 0.8rem;
  }

  .testimonials .testimonial-item .testimonial-footer .quote-icon {
    font-size: 1.3rem;
    bottom: 10px;
    right: 15px;
  }

  .testimonials .swiper-button-prev,
  .testimonials .swiper-button-next {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }

  .testimonials .swiper-button-prev::after,
  .testimonials .swiper-button-next::after {
    font-size: 14px;
  }
}

@media (max-width: 575px) {
  .testimonials .testimonial-slider {
    padding-bottom: 30px;
  }

  .testimonials .testimonial-item .testimonial-header {
    padding: 15px 15px 10px;
  }

  .testimonials .testimonial-item .testimonial-header img {
    width: 55px;
    height: 55px;
  }

  .testimonials .testimonial-item .testimonial-body {
    padding: 0 15px 10px;
  }

  .testimonials .testimonial-item .testimonial-body p {
    font-size: 0.9rem;
  }

  .testimonials .testimonial-item .testimonial-footer {
    padding: 10px 15px 15px;
  }

  .testimonials .testimonial-item .testimonial-footer h5 {
    font-size: 0.95rem;
  }

  .testimonials .testimonial-item .testimonial-footer .quote-icon {
    font-size: 1.2rem;
  }

  .testimonials .swiper-navigation {
    margin-top: 10px;
  }
}

/*--------------------------------------------------------------
# Stats Section
--------------------------------------------------------------*/
.stats .avatars {
  position: relative;
}

.stats .avatars img {
  width: 60px;
  height: 60px;
  border: 3px solid var(--accent-color);
  margin-right: -15px;
  transition: transform 0.3s ease;
}

.stats .avatars img:hover {
  transform: translateY(-5px);
  z-index: 2;
}

.stats .counters h2 {
  color: var(--heading-color);
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 10px;
}

.stats .counters h2 span {
  display: inline-block;
}

.stats .counters p {
  color: var(--default-color);
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
}

@media (max-width: 991px) {
  .stats .row {
    text-align: center;
  }

  .stats .avatars {
    justify-content: center;
    margin-bottom: 40px;
  }

  .stats .counters h2 {
    font-size: 36px;
  }

  .stats .counters p {
    font-size: 16px;
  }

  .stats .counters .col-md-4 {
    margin-bottom: 30px;
  }

  .stats .counters .col-md-4:last-child {
    margin-bottom: 0;
  }
}

@media (max-width: 576px) {
  .stats {
    padding: 60px 0;
  }

  .stats .avatars {
    flex-wrap: wrap;
    justify-content: center;
  }

  .stats .avatars img {
    margin: 0 -8px;
  }
}

/*--------------------------------------------------------------
# Pricing Section
--------------------------------------------------------------*/
.pricing {
  --card-border-radius: 20px;
}

.pricing .row {
  justify-content: center;
}

.pricing .pricing-card {
  height: 100%;
  background: var(--surface-color);
  border-radius: var(--card-border-radius);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  transition: all 0.4s;
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.pricing .pricing-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.pricing .pricing-card.popular {
  border: 2px solid var(--accent-color);
}

.pricing .pricing-card.popular .plan-cta .btn-plan {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

.pricing .pricing-card.popular .plan-cta .btn-plan:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
}

.pricing .pricing-card .popular-tag {
  position: absolute;
  top: 20px;
  right: -35px;
  background-color: var(--accent-color);
  color: var(--contrast-color);
  font-size: 13px;
  font-weight: 600;
  padding: 8px 40px;
  transform: rotate(45deg);
}

.pricing .plan-header {
  padding: 30px 30px 20px;
  text-align: center;
}

.pricing .plan-header .plan-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
}

.pricing .plan-header .plan-icon i {
  font-size: 28px;
  color: var(--accent-color);
}

.pricing .plan-header h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 10px;
}

.pricing .plan-header p {
  font-size: 15px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.pricing .plan-pricing {
  text-align: center;
  padding: 10px 30px 20px;
  position: relative;
}

.pricing .plan-pricing .currency {
  font-size: 24px;
  vertical-align: top;
  line-height: 1;
  color: var(--heading-color);
  font-weight: 600;
}

.pricing .plan-pricing .amount {
  font-size: 60px;
  font-weight: 700;
  color: var(--heading-color);
  line-height: 1;
}

.pricing .plan-pricing .period {
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
}

.pricing .plan-features {
  padding: 20px 30px;
  flex: 1;
}

.pricing .plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pricing .plan-features ul li {
  padding: 12px 0;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.pricing .plan-features ul li.disabled {
  color: color-mix(in srgb, var(--default-color), transparent 60%);
}

.pricing .plan-features ul li i {
  font-size: 18px;
}

.pricing .plan-features ul li i.bi-check-circle-fill {
  color: var(--accent-color);
}

.pricing .plan-features ul li i.bi-x-circle-fill {
  color: color-mix(in srgb, var(--default-color), transparent 60%);
}

.pricing .plan-cta {
  padding: 10px 30px 30px;
  text-align: center;
}

.pricing .plan-cta .btn-plan {
  display: inline-block;
  width: 100%;
  padding: 14px 32px;
  background-color: color-mix(in srgb, var(--default-color), transparent 95%);
  color: var(--default-color);
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s;
}

.pricing .plan-cta .btn-plan:hover {
  background-color: color-mix(in srgb, var(--default-color), transparent 80%);
}

@media (max-width: 992px) {
  .pricing .pricing-card {
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 768px) {
  .pricing .plan-pricing .amount {
    font-size: 48px;
  }

  .pricing .plan-header {
    padding: 25px 20px 15px;
  }

  .pricing .plan-features,
  .pricing .plan-pricing,
  .pricing .plan-cta {
    padding-left: 20px;
    padding-right: 20px;
  }
}

/*--------------------------------------------------------------
# Faq Section
--------------------------------------------------------------*/
.faq .faq-card {
  height: 100%;
  padding: 40px;
  background-color: color-mix(in srgb, var(--default-color), transparent 96%);
  border-radius: 8px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  text-align: center;
}

.faq .faq-card i {
  font-size: 48px;
  color: var(--accent-color);
  margin-bottom: 20px;
  display: block;
}

.faq .faq-card h3 {
  font-size: 24px;
  margin-bottom: 15px;
}

.faq .faq-card p {
  margin-bottom: 25px;
  font-size: 15px;
  line-height: 24px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.faq .faq-card .btn-primary {
  padding: 8px 20px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 50px;
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
}

.faq .faq-card .btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  border-color: color-mix(in srgb, var(--accent-color), transparent 20%);
}

.faq .faq-list {
  padding: 0;
}

.faq .faq-list .faq-item {
  position: relative;
  margin-bottom: 25px;
  padding: 30px;
  background-color: var(--surface-color);
  border-radius: 8px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  cursor: pointer;
}

.faq .faq-list .faq-item:last-child {
  margin-bottom: 0;
}

.faq .faq-list .faq-item h3 {
  font-size: 18px;
  line-height: 28px;
  font-weight: 600;
  padding-right: 40px;
  margin-bottom: 0;
  position: relative;
  cursor: pointer;
}

.faq .faq-list .faq-item .faq-content {
  display: grid;
  grid-template-rows: 0fr;
  transition: 0.3s ease-in-out;
  visibility: hidden;
  opacity: 0;
}

.faq .faq-list .faq-item .faq-content p {
  margin-bottom: 0;
  font-size: 15px;
  line-height: 24px;
  overflow: hidden;
}

.faq .faq-list .faq-item .faq-toggle {
  position: absolute;
  right: 30px;
  top: 30px;
  font-size: 24px;
  color: var(--accent-color);
  transition: transform 0.2s ease;
}

.faq .faq-list .faq-item.faq-active h3 {
  color: var(--accent-color);
}

.faq .faq-list .faq-item.faq-active .faq-content {
  grid-template-rows: 1fr;
  visibility: visible;
  opacity: 1;
  padding-top: 15px;
}

.faq .faq-list .faq-item.faq-active .faq-toggle {
  transform: rotate(45deg);
}

@media (max-width: 768px) {
  .faq .faq-list .faq-item {
    padding: 20px;
  }

  .faq .faq-list .faq-item h3 {
    font-size: 16px;
    line-height: 24px;
  }

  .faq .faq-list .faq-item .faq-toggle {
    right: 20px;
    top: 20px;
    font-size: 20px;
  }
}

/*--------------------------------------------------------------
# Team Section
--------------------------------------------------------------*/
.team .team-header {
  margin-bottom: 50px;
}

.team .team-header h2 {
  position: relative;
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
}

.team .team-header h2:before {
  content: "";
  position: absolute;
  width: 70px;
  height: 4px;
  background: var(--accent-color);
  bottom: 0;
  left: 0;
}

.team .team-header p {
  font-size: 17px;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  max-width: 600px;
}

@media (max-width: 991px) {
  .team .team-header {
    margin-bottom: 30px;
    text-align: center;
  }

  .team .team-header h2:before {
    left: 50%;
    transform: translateX(-50%);
  }

  .team .team-header p {
    margin: 0 auto;
  }

  .team .team-header .team-controls {
    margin-top: 30px;
    justify-content: center;
  }
}

.team .team-controls {
  display: flex;
  gap: 12px;
}

.team .team-controls .team-control-btn {
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 2px solid var(--accent-color);
  border-radius: 50%;
  color: var(--accent-color);
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.team .team-controls .team-control-btn:hover {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.team .team-slider {
  padding: 10px 5px 40px;
}

.team .team-slider .swiper-wrapper {
  height: auto !important;
}

.team .team-member {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  background: var(--surface-color);
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  height: 100%;
}

.team .team-member:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.team .team-member:hover .member-image .member-social {
  opacity: 1;
  bottom: 20px;
}

.team .team-member:hover .member-image:before {
  opacity: 0.8;
}

.team .team-member .member-image {
  position: relative;
  overflow: hidden;
}

.team .team-member .member-image:before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  opacity: 0;
  transition: all 0.4s ease;
  z-index: 1;
}

.team .team-member .member-image img {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.team .team-member .member-image .member-social {
  position: absolute;
  z-index: 2;
  display: flex;
  gap: 8px;
  justify-content: center;
  width: 100%;
  bottom: -30px;
  opacity: 0;
  transition: all 0.4s ease;
}

.team .team-member .member-image .member-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: var(--contrast-color);
  border-radius: 50%;
  color: var(--accent-color);
  font-size: 15px;
  transition: all 0.3s ease;
}

.team .team-member .member-image .member-social a:hover {
  background: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-3px);
}

.team .team-member .member-content {
  padding: 25px;
}

.team .team-member .member-content h3 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 5px;
}

.team .team-member .member-content span {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 15px;
}

.team .team-member .member-content p {
  font-size: 14px;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact .contact-info-section {
  height: 100%;
}

.contact .contact-info-section .info-header {
  margin-bottom: 40px;
}

.contact .contact-info-section .info-header h3 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--heading-color);
  position: relative;
  display: inline-block;
}

.contact .contact-info-section .info-header h3:after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), color-mix(in srgb, var(--accent-color), transparent 50%));
  border-radius: 2px;
}

.contact .contact-info-section .info-header p {
  font-size: 16px;
  line-height: 1.7;
  color: var(--default-color);
  margin-bottom: 0;
}

.contact .contact-info-section .contact-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 25px;
  margin-bottom: 40px;
}

.contact .contact-info-section .info-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 25px;
  background: linear-gradient(135deg, var(--surface-color), color-mix(in srgb, var(--surface-color), var(--accent-color) 3%));
  border-radius: 15px;
  border: 1px solid color-mix(in srgb, var(--accent-color), transparent 85%);
  transition: all 0.3s ease;
}

.contact .contact-info-section .info-item:hover {
  transform: translateX(10px);
  border-color: color-mix(in srgb, var(--accent-color), transparent 60%);
  box-shadow: 0 8px 25px color-mix(in srgb, var(--accent-color), transparent 85%);
}

.contact .contact-info-section .info-item:hover .info-icon {
  background-color: var(--accent-color);
}

.contact .contact-info-section .info-item:hover .info-icon i {
  color: var(--contrast-color);
}

.contact .contact-info-section .info-item .info-icon {
  width: 55px;
  height: 55px;
  flex-shrink: 0;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.contact .contact-info-section .info-item .info-icon i {
  font-size: 24px;
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.contact .contact-info-section .info-item .info-content {
  flex: 1;
}

.contact .contact-info-section .info-item .info-content h5 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--heading-color);
}

.contact .contact-info-section .info-item .info-content p {
  font-size: 15px;
  line-height: 1.6;
  color: var(--default-color);
  margin-bottom: 0;
}

.contact .contact-info-section .social-contact {
  padding: 30px;
  background: var(--surface-color);
  border-radius: 20px;
  text-align: center;
}

.contact .contact-info-section .social-contact h5 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--heading-color);
}

.contact .contact-info-section .social-contact .social-icons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.contact .contact-info-section .social-contact .social-icons .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: var(--surface-color);
  color: var(--accent-color);
  font-size: 18px;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px color-mix(in srgb, var(--accent-color), transparent 80%);
}

.contact .contact-info-section .social-contact .social-icons .social-icon:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-5px) scale(1.1);
  box-shadow: 0 8px 25px color-mix(in srgb, var(--accent-color), transparent 60%);
}

.contact .contact-form-wrapper {
  background: linear-gradient(135deg, var(--surface-color), color-mix(in srgb, var(--surface-color), var(--background-color) 50%));
  padding: 40px;
  border-radius: 25px;
  box-shadow: 0 15px 40px color-mix(in srgb, var(--default-color), transparent 90%);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.contact .contact-form-wrapper .form-header {
  text-align: center;
  margin-bottom: 35px;
}

.contact .contact-form-wrapper .form-header h3 {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--heading-color);
}

.contact .contact-form-wrapper .form-header p {
  font-size: 15px;
  line-height: 1.6;
  color: var(--default-color);
  margin-bottom: 0;
}

.contact .contact-form-wrapper .php-email-form .form-label {
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 8px;
  font-size: 14px;
}

.contact .contact-form-wrapper .php-email-form .form-control {
  height: 50px;
  padding: 12px 18px;
  border-radius: 12px;
  border: 2px solid color-mix(in srgb, var(--default-color), transparent 85%);
  background-color: var(--surface-color);
  color: var(--default-color);
  font-size: 16px;
  transition: all 0.3s ease;
}

.contact .contact-form-wrapper .php-email-form .form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--accent-color), transparent 85%);
  background-color: var(--surface-color);
  outline: none;
}

.contact .contact-form-wrapper .php-email-form .form-control::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 60%);
}

.contact .contact-form-wrapper .php-email-form .message-textarea {
  height: 120px;
  resize: vertical;
  font-family: inherit;
}

.contact .contact-form-wrapper .php-email-form .submit-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), #000 20%));
  color: var(--contrast-color);
  border: none;
  padding: 18px 30px;
  border-radius: 15px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact .contact-form-wrapper .php-email-form .submit-btn:before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, color-mix(in srgb, var(--contrast-color), transparent 80%), transparent);
  transition: left 0.5s ease;
}

.contact .contact-form-wrapper .php-email-form .submit-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px color-mix(in srgb, var(--accent-color), transparent 70%);
}

.contact .contact-form-wrapper .php-email-form .submit-btn:hover:before {
  left: 100%;
}

.contact .contact-form-wrapper .php-email-form .submit-btn:hover i {
  transform: translateX(5px);
}

.contact .contact-form-wrapper .php-email-form .submit-btn span {
  position: relative;
  z-index: 1;
}

.contact .contact-form-wrapper .php-email-form .submit-btn i {
  font-size: 18px;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
}

@media (max-width: 992px) {
  .contact .contact-info-section {
    margin-bottom: 40px;
  }

  .contact .contact-info-section .contact-info-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .contact .contact-info-section .contact-info-grid {
    grid-template-columns: 1fr;
  }

  .contact .contact-info-section .info-item {
    padding: 20px;
  }

  .contact .contact-info-section .info-item:hover {
    transform: translateY(-5px);
  }

  .contact .contact-form-wrapper {
    padding: 30px 25px;
  }

  .contact .contact-form-wrapper .form-header h3 {
    font-size: 24px;
  }
}

/*--------------------------------------------------------------
# Service Details Section
--------------------------------------------------------------*/
.service-details .service-hero {
  position: relative;
  margin-bottom: 40px;
  border-radius: 12px;
  overflow: hidden;
}

.service-details .service-hero img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.service-details .service-hero .service-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
}

.service-details .service-content .service-header {
  margin-bottom: 40px;
}

.service-details .service-content .service-header h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--heading-color);
}

.service-details .service-content .service-header .service-intro {
  font-size: 18px;
  line-height: 1.7;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.service-details .service-content h4 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 30px;
  color: var(--heading-color);
}

.service-details .service-features {
  margin-bottom: 50px;
}

.service-details .service-features .feature-item {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  background: var(--surface-color);
  border-radius: 8px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  transition: all 0.3s ease;
}

.service-details .service-features .feature-item:hover {
  box-shadow: 0 8px 25px color-mix(in srgb, var(--default-color), transparent 90%);
  transform: translateY(-2px);
}

.service-details .service-features .feature-item .feature-icon {
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.service-details .service-features .feature-item .feature-icon i {
  font-size: 24px;
  color: var(--accent-color);
}

.service-details .service-features .feature-item .feature-content h5 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--heading-color);
}

.service-details .service-features .feature-item .feature-content p {
  font-size: 14px;
  margin: 0;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.service-details .service-process {
  margin-bottom: 50px;
}

.service-details .service-process .process-steps .process-step {
  display: flex;
  margin-bottom: 30px;
  position: relative;
}

.service-details .service-process .process-steps .process-step:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 25px;
  top: 60px;
  width: 2px;
  height: 40px;
  background: color-mix(in srgb, var(--accent-color), transparent 70%);
}

.service-details .service-process .process-steps .process-step .step-number {
  background: var(--accent-color);
  color: var(--contrast-color);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  margin-right: 25px;
  flex-shrink: 0;
  font-size: 16px;
}

.service-details .service-process .process-steps .process-step .step-content h5 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--heading-color);
}

.service-details .service-process .process-steps .process-step .step-content p {
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.service-details .service-gallery {
  margin-bottom: 30px;
}

.service-details .service-gallery img {
  transition: transform 0.3s ease;
}

.service-details .service-gallery img:hover {
  transform: scale(1.05);
}

.service-details .service-sidebar .service-menu {
  background: var(--surface-color);
  padding: 30px;
  border-radius: 10px;
  margin-bottom: 30px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.service-details .service-sidebar .service-menu h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 25px;
  color: var(--heading-color);
}

.service-details .service-sidebar .service-menu .menu-list .menu-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  color: var(--default-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

.service-details .service-sidebar .service-menu .menu-list .menu-item:last-child {
  border-bottom: none;
}

.service-details .service-sidebar .service-menu .menu-list .menu-item:hover,
.service-details .service-sidebar .service-menu .menu-list .menu-item.active {
  color: var(--accent-color);
  padding-left: 10px;
}

.service-details .service-sidebar .service-menu .menu-list .menu-item:hover i,
.service-details .service-sidebar .service-menu .menu-list .menu-item.active i {
  color: var(--accent-color);
}

.service-details .service-sidebar .service-menu .menu-list .menu-item i {
  margin-right: 12px;
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  transition: color 0.3s ease;
}

.service-details .service-sidebar .service-menu .menu-list .menu-item span {
  font-weight: 500;
}

.service-details .service-sidebar .service-info {
  background: var(--surface-color);
  padding: 30px;
  border-radius: 10px;
  margin-bottom: 30px;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.service-details .service-sidebar .service-info h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 25px;
  color: var(--heading-color);
}

.service-details .service-sidebar .service-info .info-list .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.service-details .service-sidebar .service-info .info-list .info-item:last-child {
  border-bottom: none;
}

.service-details .service-sidebar .service-info .info-list .info-item .info-label {
  font-weight: 500;
  color: var(--default-color);
}

.service-details .service-sidebar .service-info .info-list .info-item .info-value {
  font-weight: 600;
  color: var(--accent-color);
}

.service-details .service-sidebar .contact-card {
  background: linear-gradient(135deg, var(--accent-color), color-mix(in srgb, var(--accent-color), #000 20%));
  padding: 30px;
  border-radius: 10px;
  color: var(--contrast-color);
}

.service-details .service-sidebar .contact-card .contact-content h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--contrast-color);
}

.service-details .service-sidebar .contact-card .contact-content p {
  margin-bottom: 20px;
  opacity: 0.9;
  font-size: 14px;
  line-height: 1.6;
}

.service-details .service-sidebar .contact-card .contact-content .contact-info {
  margin-bottom: 25px;
}

.service-details .service-sidebar .contact-card .contact-content .contact-info .contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.service-details .service-sidebar .contact-card .contact-content .contact-info .contact-item i {
  margin-right: 10px;
  opacity: 0.8;
}

.service-details .service-sidebar .contact-card .contact-content .contact-info .contact-item span {
  font-size: 14px;
}

.service-details .service-sidebar .contact-card .contact-content .btn {
  background: var(--contrast-color);
  color: var(--accent-color);
  border: none;
  padding: 12px 25px;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.service-details .service-sidebar .contact-card .contact-content .btn:hover {
  background: color-mix(in srgb, var(--contrast-color), transparent 10%);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .service-details .service-hero img {
    height: 250px;
  }

  .service-details .service-hero .service-badge {
    top: 15px;
    right: 15px;
    padding: 6px 15px;
    font-size: 12px;
  }

  .service-details .service-content .service-header h2 {
    font-size: 24px;
  }

  .service-details .feature-item {
    padding: 15px !important;
  }

  .service-details .feature-item .feature-icon {
    width: 50px !important;
    height: 50px !important;
    margin-right: 15px !important;
  }

  .service-details .feature-item .feature-icon i {
    font-size: 20px !important;
  }

  .service-details .service-sidebar {
    margin-top: 30px;
  }
}

/*--------------------------------------------------------------
# Starter Section Section
--------------------------------------------------------------*/
.starter-section {
  /* Add your styles here */
}