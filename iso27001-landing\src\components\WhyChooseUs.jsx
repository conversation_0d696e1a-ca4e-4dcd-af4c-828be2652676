import React from 'react'

const WhyChooseUs = () => {
  const reasons = [
    {
      title: "มีทีมที่ปรึกษามืออาชีพ",
      description: "Certified ISO 27001 Lead Auditor / Lead Implementer",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      )
    },
    {
      title: "ประสบการณ์จริงในองค์กร B2B",
      description: "ครอบคลุม FinTech, Telco, HealthTech, Logistics",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      )
    },
    {
      title: "ดูแลครบวงจร",
      description: "ตั้งแต่ประเมิน วางระบบ ฝึกอบรม จนถึงเตรียมรับการรับรอง",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      )
    },
    {
      title: "วัดผลชัดเจน",
      description: "ส่งมอบผลลัพธ์เป็น Milestone พร้อมรายงานต่อผู้บริหาร",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    }
  ]

  return (
    <section id="why-us" className="py-20 bg-gradient-to-br from-primary-50 to-primary-100">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-gray-900">
            ทำไมองค์กรชั้นนำเลือก ACIS?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            เป็นที่ปรึกษา ISO/IEC 27001 ที่ได้รับความไว้วางใจจากองค์กรชั้นนำ
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {reasons.map((reason, index) => (
            <div key={index} className="bg-white rounded-2xl p-8 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-primary-500 text-white rounded-full flex items-center justify-center mx-auto mb-6">
                {reason.icon}
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-4">{reason.title}</h3>
              <p className="text-gray-600">{reason.description}</p>
            </div>
          ))}
        </div>

        {/* Success Stories */}
        <div className="bg-white rounded-2xl p-8 shadow-lg">
          <h3 className="text-2xl font-bold text-center mb-8 text-gray-900">
            องค์กรที่ไว้วางใจ ACIS
          </h3>
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div className="p-6">
              <div className="text-3xl font-bold text-primary-600 mb-2">50+</div>
              <div className="text-gray-600">องค์กรที่ผ่านการรับรอง</div>
            </div>
            <div className="p-6">
              <div className="text-3xl font-bold text-primary-600 mb-2">95%</div>
              <div className="text-gray-600">อัตราความสำเร็จ</div>
            </div>
            <div className="p-6">
              <div className="text-3xl font-bold text-primary-600 mb-2">10+</div>
              <div className="text-gray-600">ปีประสบการณ์</div>
            </div>
            <div className="p-6">
              <div className="text-3xl font-bold text-primary-600 mb-2">24/7</div>
              <div className="text-gray-600">การสนับสนุน</div>
            </div>
          </div>
        </div>

        {/* Industries */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold mb-8 text-gray-900">อุตสาหกรรมที่เราให้บริการ</h3>
          <div className="flex flex-wrap justify-center gap-4">
            {['FinTech', 'Telco', 'HealthTech', 'Logistics', 'E-commerce', 'Manufacturing', 'Government', 'Education'].map((industry, index) => (
              <span key={index} className="bg-primary-500 text-white px-6 py-3 rounded-full font-medium">
                {industry}
              </span>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default WhyChooseUs
