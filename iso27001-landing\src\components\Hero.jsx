import React from 'react'

const Hero = () => {
  return (
    <div className="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white">
      {/* Navigation */}
      <nav className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold">ACIS Professional Center</div>
          <div className="hidden md:flex space-x-8">
            <a href="#about" className="hover:text-primary-200 transition-colors">เกี่ยวกับเรา</a>
            <a href="#services" className="hover:text-primary-200 transition-colors">บริการ</a>
            <a href="#why-us" className="hover:text-primary-200 transition-colors">ทำไมเลือกเรา</a>
            <a href="#contact" className="hover:text-primary-200 transition-colors">ติดต่อ</a>
          </div>
        </div>
      </nav>

      {/* Hero Content */}
      <div className="container mx-auto px-6 py-20">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              ความสำคัญของ <span className="text-yellow-400">ISO/IEC 27001</span> สำหรับธุรกิจ
            </h1>
            <p className="text-xl mb-8 text-primary-100">
              เราช่วยองค์กรของคุณออกแบบระบบบริหารจัดการความมั่นคงสารสนเทศ (ISMS) 
              ที่สอดคล้องกับมาตรฐาน ISO/IEC 27001:2022
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-yellow-400 text-primary-900 px-8 py-4 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
                ปรึกษาฟรี
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary-900 transition-colors">
                ดูบริการ
              </button>
            </div>
          </div>
          <div className="relative">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span>Confidentiality (ความลับของข้อมูล)</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span>Integrity (ความถูกต้องสมบูรณ์)</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span>Availability (ความพร้อมใช้งาน)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Hero
