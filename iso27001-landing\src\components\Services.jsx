import React from 'react'

const Services = () => {
  const services = [
    {
      title: "Gap Analysis & Readiness Assessment",
      description: "ประเมินสถานะปัจจุบันขององค์กรเทียบกับ ISO/IEC 27001:2022",
      features: [
        "จัดทำรายงาน GAP Report พร้อมแผนการดำเนินการ (Roadmap)",
        "ประเมินความพร้อมขององค์กร",
        "วิเคราะห์จุดอ่อนและข้อเสนอแนะ"
      ],
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    },
    {
      title: "Documentation & System Development",
      description: "พัฒนาเอกสารและระบบครบชุดตามมาตรฐาน",
      features: [
        "พัฒนาเอกสารครบชุด: ISMS Manual, Policy, SOPs, Risk Register",
        "ให้คำแนะนำด้านเทคนิค เช่น Backup, Logging, Incident Response",
        "ออกแบบระบบที่เหมาะสมกับองค์กร"
      ],
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      title: "In-house Training & Awareness",
      description: "อบรมและสร้างความตรองรู้ให้กับทีมงาน",
      features: [
        "หลักสูตร Internal Auditor, Implementer สำหรับทีมภายใน",
        "จัดอบรมพนักงานทุกระดับให้เข้าใจระบบ ISMS",
        "สร้างวัฒนธรรมองค์กรที่ให้ความสำคัญกับข้อมูล"
      ],
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      )
    },
    {
      title: "Audit & Certification Support",
      description: "สนับสนุนการตรวจสอบและรับรอง",
      features: [
        "การ Audit ตามมาตรฐาน Certification Bodies",
        "ติดตามการแก้ไข (Corrective Action) ก่อน Audit จริง",
        "แนะนำหน่วยตรวจที่เหมาะสม เช่น BSI, SGS, TUV"
      ],
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
        </svg>
      )
    }
  ]

  return (
    <section id="services" className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-gray-900">
            บริการของเราที่ปรึกษา ISO/IEC 27001
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            ครอบคลุมตั้งแต่เอกสาร นโยบาย การอบรม ไปจนถึงการเตรียมสอบรับรอง 
            พร้อมคำแนะนำจากทีมที่ปรึกษาที่ได้รับการรับรองระดับสากล
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {services.map((service, index) => (
            <div key={index} className="bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-primary-500 text-white rounded-xl flex items-center justify-center mr-4">
                  {service.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900">{service.title}</h3>
              </div>
              
              <p className="text-gray-600 mb-6">{service.description}</p>
              
              <ul className="space-y-3">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Services
