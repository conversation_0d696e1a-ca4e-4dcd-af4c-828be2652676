{"name": "vite", "version": "5.0.0", "type": "module", "license": "MIT", "author": "<PERSON>", "description": "Native-ESM powered web dev build tool", "bin": {"vite": "bin/vite.js"}, "keywords": ["frontend", "framework", "hmr", "dev-server", "build-tool", "vite"], "main": "./dist/node/index.js", "types": "./dist/node/index.d.ts", "exports": {".": {"import": {"types": "./dist/node/index.d.ts", "default": "./dist/node/index.js"}, "require": {"types": "./index.d.cts", "default": "./index.cjs"}}, "./client": {"types": "./client.d.ts"}, "./dist/client/*": "./dist/client/*", "./types/*": {"types": "./types/*"}, "./package.json": "./package.json"}, "files": ["bin", "dist", "client.d.ts", "index.cjs", "index.d.cts", "types"], "engines": {"node": "^18.0.0 || >=20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/vite"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/#readme", "funding": "https://github.com/vitejs/vite?sponsor=1", "//": "READ CONTRIBUTING.md to understand what to put under deps vs. devDeps!", "dependencies": {"esbuild": "^0.19.3", "postcss": "^8.4.31", "rollup": "^4.2.0"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "devDependencies": {"@ampproject/remapping": "^2.2.1", "@babel/parser": "^7.23.3", "@jridgewell/trace-mapping": "^0.3.20", "@rollup/plugin-alias": "^5.0.1", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-dynamic-import-vars": "^2.1.0", "@rollup/plugin-json": "^6.0.1", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@rollup/pluginutils": "^5.0.5", "@types/escape-html": "^1.0.4", "@types/pnpapi": "^0.0.5", "acorn": "^8.11.2", "acorn-walk": "^8.3.0", "cac": "^6.7.14", "chokidar": "^3.5.3", "connect": "^3.7.0", "convert-source-map": "^2.0.0", "cors": "^2.8.5", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "dep-types": "link:./src/types", "dotenv": "^16.3.1", "dotenv-expand": "^10.0.0", "es-module-lexer": "^1.4.1", "escape-html": "^1.0.3", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "http-proxy": "^1.18.1", "json-stable-stringify": "^1.0.2", "launch-editor-middleware": "^2.6.1", "lightningcss": "^1.22.1", "magic-string": "^0.30.5", "micromatch": "^4.0.5", "mlly": "^1.4.2", "mrmime": "^1.0.1", "okie": "^1.0.1", "open": "^8.4.2", "parse5": "^7.1.2", "periscopic": "^4.0.2", "picocolors": "^1.0.0", "picomatch": "^2.3.1", "postcss-import": "^15.1.0", "postcss-load-config": "^4.0.1", "postcss-modules": "^6.0.0", "resolve.exports": "^2.0.2", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-license": "^3.2.0", "sirv": "^2.0.3", "source-map-support": "^0.5.21", "strip-ansi": "^7.1.0", "strip-literal": "^1.3.0", "tsconfck": "^3.0.0", "tslib": "^2.6.2", "types": "link:./types", "ufo": "^1.3.1", "ws": "^8.14.2"}, "peerDependencies": {"@types/node": "^18.0.0 || >=20.0.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "less": {"optional": true}, "sugarss": {"optional": true}, "lightningcss": {"optional": true}, "terser": {"optional": true}}, "scripts": {"dev": "rimraf dist && pnpm run build-bundle -w", "build": "rimraf dist && run-s build-bundle build-types", "build-bundle": "rollup --config rollup.config.ts --configPlugin typescript", "build-types": "run-s build-types-temp build-types-roll build-types-check", "build-types-temp": "tsc --emitDeclarationOnly --outDir temp/node -p src/node", "build-types-roll": "rollup --config rollup.dts.config.ts --configPlugin typescript && rimraf temp", "build-types-check": "tsc --project tsconfig.check.json", "typecheck": "tsc --noEmit", "lint": "eslint --cache --ext .ts src/**", "format": "prettier --write --cache --parser typescript \"src/**/*.ts\""}}